<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>零代码工作流节点配置</title>
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>零代码工作流节点配置</h1>
            <p>可视化配置工作流节点，支持数据操作、页面交互、表单处理等多种节点类型</p>
        </div>

        <div class="main-content">
            <!-- 搜索栏 -->
            <div class="search-container">
                <div class="search-box">
                    <input type="text" id="node-search" placeholder="搜索节点、插件、工作流" class="search-input">
                    <span class="search-icon">🔍</span>
                </div>
            </div>
            
            <!-- 节点卡片网格 -->
            <div class="node-grid">
                <!-- 数据操作分类 -->
                <div class="category-section" data-category="data">
                    <div class="category-header">
                        <h3>数据操作</h3>
                    </div>
                    <div class="node-cards">
                        <div class="node-card" onclick="selectNode('data-create')" data-node="data-create">
                            <div class="node-icon">📝</div>
                            <div class="node-text">新增数据</div>
                        </div>
                        <div class="node-card" onclick="selectNode('data-update')" data-node="data-update">
                            <div class="node-icon">✏️</div>
                            <div class="node-text">更新数据</div>
                        </div>
                        <div class="node-card" onclick="selectNode('data-query')" data-node="data-query">
                            <div class="node-icon">🔍</div>
                            <div class="node-text">查询数据</div>
                        </div>
                        <div class="node-card" onclick="selectNode('data-delete')" data-node="data-delete">
                            <div class="node-icon">🗑️</div>
                            <div class="node-text">删除数据</div>
                        </div>
                    </div>
                </div>
                
                <!-- 页面操作分类 -->
                <div class="category-section" data-category="page">
                    <div class="category-header">
                        <h3>页面操作</h3>
                    </div>
                    <div class="node-cards">
                        <div class="node-card" onclick="selectNode('page-message')" data-node="page-message">
                            <div class="node-icon">💬</div>
                            <div class="node-text">创建消息</div>
                        </div>
                        <div class="node-card" onclick="selectNode('input-set-value')" data-node="input-set-value">
                            <div class="node-icon">🎯</div>
                            <div class="node-text">设置值</div>
                        </div>
                        <div class="node-card" onclick="selectNode('input-set-property')" data-node="input-set-property">
                            <div class="node-icon">🔧</div>
                            <div class="node-text">设置属性</div>
                        </div>
                        <div class="node-card" onclick="selectNode('input-visibility')" data-node="input-visibility">
                            <div class="node-icon">👁️</div>
                            <div class="node-text">显示隐藏</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 抽屉遮罩层 -->
        <div id="drawer-overlay" class="drawer-overlay" onclick="closeDrawer()"></div>

        <!-- 右侧抽屉配置面板 -->
        <div id="config-drawer" class="config-drawer">
            <div class="config-panel-header">
                <span>节点配置</span>
                <button class="drawer-close-btn" onclick="closeDrawer()" type="button">×</button>
            </div>
            <div class="config-panel-content">
                <!-- 默认空状态 -->
                <div id="empty-state" class="empty-state">
                    <h3>请选择节点类型</h3>
                    <p>从左侧选择一个节点类型开始配置</p>
                </div>

                <!-- 节点配置内容将在这里动态加载 -->
                <div id="node-config-content"></div>
            </div>
        </div>
    </div>

    <!-- JavaScript 文件引用 -->
    <script src="js/form-data.js"></script>
    <script src="js/node-config.js"></script>
    <script src="js/ui-interaction.js"></script>
    <script src="js/main.js"></script>
</body>

</html>