<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输出参数功能测试</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>输出参数功能测试</h1>
            <p>测试输出参数的变量名称输入功能</p>
        </div>
        
        <div class="main-content">
            <div class="node-list">
                <div class="node-list-header">
                    测试节点
                </div>
                <div class="node-list-content">
                    <div class="node-item" onclick="selectNode('data-create')" data-node="data-create">新增数据</div>
                    <div class="node-item" onclick="selectNode('data-query')" data-node="data-query">查询数据</div>
                </div>
            </div>
        </div>
        
        <!-- 抽屉遮罩层 -->
        <div id="drawer-overlay" class="drawer-overlay" onclick="closeDrawer()"></div>
        
        <!-- 右侧抽屉配置面板 -->
        <div id="config-drawer" class="config-drawer">
            <div class="config-panel-header">
                <span>节点配置</span>
                <button class="drawer-close-btn" onclick="closeDrawer()" type="button">×</button>
            </div>
            <div class="config-panel-content">
                <!-- 默认空状态 -->
                <div id="empty-state" class="empty-state">
                    <h3>请选择节点类型</h3>
                    <p>从左侧选择一个节点类型开始配置</p>
                </div>
                
                <!-- 节点配置内容将在这里动态加载 -->
                <div id="node-config-content"></div>
                
                <!-- 测试区域 -->
                <div class="test-section" style="display: none;">
                    <h3>功能测试</h3>
                    <div class="form-group full-width">
                        <label>测试表达式</label>
                        <div class="expression-validator">
                            <input type="text" id="test-expression" class="form-control" placeholder="输入测试表达式，例如: {{result.id}}"
                                oninput="validateExpressionInput(this); handleExpressionInput(this)"
                                onfocus="showExpressionHelp(this)"
                                onblur="hideExpressionValidation(this)"
                                onkeydown="handleExpressionKeydown(event, this)">
                            <div class="expression-help" onclick="toggleExpressionHelp(this)">?</div>
                            <div class="expression-help-tooltip">
                                支持使用{{}}引用变量，例如：<br>
                                • {{form.name}} - 表单字段<br>
                                • {{query.id}} - 查询参数<br>
                                • {{result.count}} - 执行结果
                            </div>
                            <div class="expression-validation" id="validation-test-expression"></div>
                            <div class="autocomplete-popup" id="autocomplete-test-expression"></div>
                        </div>
                    </div>
                    
                    <div class="form-group full-width">
                        <label>获取变量配置</label>
                        <button type="button" onclick="getVariableConfig()" class="btn">获取当前变量配置</button>
                        <div id="variable-config-result" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript 文件引用 -->
    <script src="js/form-data.js"></script>
    <script src="js/expression-helper.js"></script>
    <script src="js/node-config.js"></script>
    <script src="js/ui-interaction.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        // 重写showNodeConfig函数以添加测试区域
        const originalShowNodeConfig = showNodeConfig;
        showNodeConfig = function(nodeType) {
            originalShowNodeConfig(nodeType);
            
            // 显示测试区域
            setTimeout(() => {
                const testSection = document.querySelector('.test-section');
                if (testSection && nodeType.startsWith('data-')) {
                    testSection.style.display = 'block';
                }
            }, 100);
        };
        
        // 获取变量配置
        function getVariableConfig() {
            const config = getOutputParamsConfig();
            const resultDiv = document.getElementById('variable-config-result');
            
            if (Object.keys(config).length === 0) {
                resultDiv.innerHTML = '<span style="color: #666;">暂无变量配置</span>';
                return;
            }
            
            let html = '<strong>当前变量配置：</strong><br>';
            Object.keys(config).forEach(key => {
                html += `${key}: ${config[key]}<br>`;
            });
            
            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>